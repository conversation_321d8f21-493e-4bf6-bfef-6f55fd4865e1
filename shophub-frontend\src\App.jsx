import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useNavigate } from 'react-router-dom';
import './App.css';

// Components
import Register from './components/Register';
import Login from './components/Login';
import ProductList from './components/ProductList';
import ProductDetail from './components/ProductDetail';
import Cart from './components/Cart';
import CustomerDashboard from './components/CustomerDashboard';
import BusinessOwnerDashboard from './components/BusinessOwnerDashboard';

function App() {
  const [user, setUser] = useState(null);
  const [cartCount, setCartCount] = useState(0);

  useEffect(() => {
    // Check for stored user data on initial load
    const storedUser = JSON.parse(localStorage.getItem('user'));
    if (storedUser) {
      setUser(storedUser);
      fetchCartCount(storedUser);
    }
  }, []);

  const fetchCartCount = async (userData) => {
    if (userData && userData.role === 'CUSTOMER') {
      try {
        const response = await fetch(`http://localhost:8080/api/cart/${userData.id}`);
        if (response.ok) {
          const cartItems = await response.json();
          const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
          setCartCount(totalItems);
        }
      } catch (error) {
        console.error('Error fetching cart count:', error);
      }
    }
  };

  const handleLogin = (userData) => {
    setUser(userData);
    localStorage.setItem('user', JSON.stringify(userData));
    fetchCartCount(userData);
  };

  const handleLogout = () => {
    setUser(null);
    setCartCount(0);
    localStorage.removeItem('user');
  };

  const updateCartCount = () => {
    if (user) {
      fetchCartCount(user);
    }
  };

  return (
    <Router>
      <div className="App">
        <nav>
          <div className="nav-container">
            <Link to="/" className="logo">ShopHub</Link>
            <div className="nav-links">
              {user ? (
                <>
                  <span className="user-info">Welcome, {user.username} ({user.role})</span>
                  {user.role === 'CUSTOMER' && (
                    <>
                      <Link to="/cart">Cart ({cartCount})</Link>
                      <Link to="/dashboard">Dashboard</Link>
                    </>
                  )}
                  {user.role === 'BUSINESS_OWNER' && (
                    <Link to="/business-dashboard">Business Dashboard</Link>
                  )}
                  <button onClick={handleLogout}>Logout</button>
                </>
              ) : (
                <>
                  <Link to="/login">Login</Link>
                  <Link to="/register">Register</Link>
                </>
              )}
            </div>
          </div>
        </nav>

        <main>
          <Routes>
            <Route path="/" element={<ProductList user={user} onCartUpdate={updateCartCount} />} />
            <Route path="/register" element={<Register />} />
            <Route path="/login" element={<Login onLogin={handleLogin} />} />
            <Route path="/products/:id" element={<ProductDetail user={user} onCartUpdate={updateCartCount} />} />
            <Route path="/cart" element={user && user.role === 'CUSTOMER' ? <Cart user={user} onCartUpdate={updateCartCount} /> : <div className="form-container"><p>Please login as a customer to view your cart.</p></div>} />
            <Route path="/dashboard" element={user && user.role === 'CUSTOMER' ? <CustomerDashboard user={user} /> : <div className="form-container"><p>Access Denied</p></div>} />
            <Route path="/business-dashboard" element={user && user.role === 'BUSINESS_OWNER' ? <BusinessOwnerDashboard user={user} /> : <div className="form-container"><p>Access Denied</p></div>} />
          </Routes>
        </main>

        <footer>
          <p>&copy; 2025 ShopHub. Contact +250785910639, Kigali, Kicukiro District, Rwanda.</p>
        </footer>
      </div>
    </Router>
  );
}

export default App;

