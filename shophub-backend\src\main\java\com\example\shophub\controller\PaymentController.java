package com.example.shophub.controller;

import com.example.shophub.model.Order;
import com.example.shophub.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/payment")
public class PaymentController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/process")
    public ResponseEntity<String> processPayment(@RequestParam Long orderId) {
        // Simulate payment processing
        boolean paymentSuccessful = Math.random() > 0.5; // Simulate success/failure

        if (paymentSuccessful) {
            orderService.updateOrderStatus(orderId, "Paid");
            // In a real application, send notification to owner here
            System.out.println("Owner Notification: Order " + orderId + " has been paid.");
            return ResponseEntity.ok("Payment successful for order " + orderId);
        } else {
            orderService.updateOrderStatus(orderId, "Payment Failed");
            System.out.println("Owner Notification: Payment failed for order " + orderId + ".");
            return ResponseEntity.badRequest().body("Payment failed for order " + orderId);
        }
    }
}


