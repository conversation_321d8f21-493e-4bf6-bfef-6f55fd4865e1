import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const CustomerDashboard = ({ user }) => {
  const [cartItems, setCartItems] = useState([]);
  const [recentOrders] = useState([
    { id: 1, date: '2025-01-20', total: 1200.00, status: 'Delivered' },
    { id: 2, date: '2025-01-15', total: 150.00, status: 'Shipped' },
    { id: 3, date: '2025-01-10', total: 75.00, status: 'Processing' }
  ]);

  useEffect(() => {
    if (user) {
      fetchCartItems();
    }
  }, [user]);

  const fetchCartItems = async () => {
    try {
      const response = await fetch(`http://localhost:8080/api/cart/${user.id}`);
      if (response.ok) {
        const data = await response.json();
        setCartItems(data);
      }
    } catch (error) {
      console.error('Error fetching cart items:', error);
    }
  };

  const cartTotal = cartItems.reduce((sum, item) => {
    return sum + (item.product.price * item.quantity);
  }, 0);

  if (!user || user.role !== 'CUSTOMER') {
    return <div className="text-center">Access denied. Please login as a customer.</div>;
  }

  return (
    <div className="max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Customer Dashboard</h1>
      
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-semibold mb-4">Welcome back, {user.username}!</h2>
        <p className="text-gray-600">Manage your orders, view your cart, and explore new products.</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cart Summary */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Cart Summary</h3>
          {cartItems.length > 0 ? (
            <>
              <p className="text-gray-600 mb-2">{cartItems.length} item(s) in cart</p>
              <p className="text-2xl font-bold text-blue-600 mb-4">${cartTotal.toFixed(2)}</p>
              <div className="space-y-2 mb-4">
                {cartItems.slice(0, 3).map(item => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>{item.product.name} (x{item.quantity})</span>
                    <span>${(item.product.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
                {cartItems.length > 3 && (
                  <p className="text-sm text-gray-500">...and {cartItems.length - 3} more items</p>
                )}
              </div>
              <Link
                to="/cart"
                className="block w-full bg-blue-500 hover:bg-blue-700 text-white text-center py-2 px-4 rounded"
              >
                View Full Cart
              </Link>
            </>
          ) : (
            <>
              <p className="text-gray-500 mb-4">Your cart is empty</p>
              <Link
                to="/"
                className="block w-full bg-blue-500 hover:bg-blue-700 text-white text-center py-2 px-4 rounded"
              >
                Start Shopping
              </Link>
            </>
          )}
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Recent Orders</h3>
          {recentOrders.length > 0 ? (
            <div className="space-y-3">
              {recentOrders.map(order => (
                <div key={order.id} className="border-b border-gray-200 pb-3 last:border-b-0">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-semibold">Order #{order.id}</p>
                      <p className="text-sm text-gray-600">{order.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">${order.total.toFixed(2)}</p>
                      <span className={`text-xs px-2 py-1 rounded ${
                        order.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                        order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No orders yet</p>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 className="text-xl font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/"
            className="bg-blue-500 hover:bg-blue-700 text-white text-center py-3 px-4 rounded"
          >
            Browse Products
          </Link>
          <Link
            to="/cart"
            className="bg-green-500 hover:bg-green-700 text-white text-center py-3 px-4 rounded"
          >
            View Cart
          </Link>
          <button className="bg-gray-500 hover:bg-gray-700 text-white py-3 px-4 rounded">
            Order History
          </button>
        </div>
      </div>

      {/* Account Information */}
      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 className="text-xl font-semibold mb-4">Account Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Username</p>
            <p className="font-semibold">{user.username}</p>
          </div>
          <div>
            <p className="text-gray-600">Account Type</p>
            <p className="font-semibold">{user.role}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDashboard;

