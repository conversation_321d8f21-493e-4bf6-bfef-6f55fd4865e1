import React, { useState, useEffect } from 'react';

const BusinessOwnerDashboard = ({ user }) => {
  const [products, setProducts] = useState([]);
  const [analytics] = useState({
    totalSales: 15420.50,
    totalOrders: 127,
    totalCustomers: 89,
    averageOrderValue: 121.42
  });

  const [recentOrders] = useState([
    { id: 1, customer: 'Isingizwe', date: '2025-01-25', total: 1200.00, status: 'Processing' },
    { id: 2, customer: '<PERSON>', date: '2025-01-24', total: 150.00, status: 'Shipped' },
    { id: 3, customer: '<PERSON>', date: '2025-01-23', total: 75.00, status: 'Delivered' },
    { id: 4, customer: '<PERSON>', date: '2025-01-22', total: 300.00, status: 'Processing' }
  ]);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const handleStockChange = async (productId, newStock) => {
    try {
      const response = await fetch(`http://localhost:8080/api/products/${productId}/stock?newStock=${newStock}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        alert('Stock updated successfully!');
        fetchProducts(); // Refresh product list
      } else {
        alert('Failed to update stock');
      }
    } catch (error) {
      console.error('Error updating stock:', error);
      alert('Failed to update stock');
    }
  };

  if (!user || user.role !== 'BUSINESS_OWNER') {
    return <div className="text-center">Access denied. Please login as a business owner.</div>;
  }

  return (
    <div className="max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Business Owner Dashboard</h1>
      
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-semibold mb-4">Welcome back, {user.username}!</h2>
        <p className="text-gray-600">Monitor your business performance and manage your e-commerce operations.</p>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Total Sales</h3>
          <p className="text-3xl font-bold text-green-600">${analytics.totalSales.toFixed(2)}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Total Orders</h3>
          <p className="text-3xl font-bold text-blue-600">{analytics.totalOrders}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Total Customers</h3>
          <p className="text-3xl font-bold text-purple-600">{analytics.totalCustomers}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Avg Order Value</h3>
          <p className="text-3xl font-bold text-orange-600">${analytics.averageOrderValue.toFixed(2)}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Recent Orders</h3>
          <div className="space-y-3">
            {recentOrders.map(order => (
              <div key={order.id} className="border-b border-gray-200 pb-3 last:border-b-0">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-semibold">Order #{order.id}</p>
                    <p className="text-sm text-gray-600">Customer: {order.customer}</p>
                    <p className="text-sm text-gray-600">{order.date}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">${order.total.toFixed(2)}</p>
                    <span className={`text-xs px-2 py-1 rounded ${
                      order.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                      order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Product Inventory */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Product Inventory</h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {products.map(product => (
              <div key={product.id} className="flex justify-between items-center border-b border-gray-200 pb-2 last:border-b-0">
                <div>
                  <p className="font-semibold text-sm">{product.name}</p>
                  <p className="text-xs text-gray-600">${product.price}</p>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm px-2 py-1 rounded ${
                    product.stock > 20 ? 'bg-green-100 text-green-800' :
                    product.stock > 10 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {product.stock} in stock
                  </span>
                  <input
                    type="number"
                    defaultValue={product.stock}
                    onBlur={(e) => handleStockChange(product.id, parseInt(e.target.value))}
                    className="w-20 p-1 border border-gray-300 rounded text-sm"
                  />
                  <button
                    onClick={() => handleStockChange(product.id, parseInt(document.querySelector(`input[data-product-id='${product.id}']`).value))}
                    className="bg-blue-500 hover:bg-blue-700 text-white py-1 px-2 rounded text-sm"
                    data-product-id={product.id}
                  >
                    Update
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Business Information */}
      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 className="text-xl font-semibold mb-4">Business Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold mb-2">Contact Information</h4>
            <p className="text-gray-600">Phone: +************</p>
            <p className="text-gray-600">Location: Kigali, Kicukiro District, Rwanda</p>
            <p className="text-gray-600">Email: <EMAIL></p>
          </div>
          <div>
            <h4 className="font-semibold mb-2">Account Details</h4>
            <p className="text-gray-600">Username: {user.username}</p>
            <p className="text-gray-600">Role: {user.role}</p>
            <p className="text-gray-600">Business Type: E-commerce Platform</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 className="text-xl font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button className="bg-blue-500 hover:bg-blue-700 text-white py-3 px-4 rounded">
            Add Product
          </button>
          <button className="bg-green-500 hover:bg-green-700 text-white py-3 px-4 rounded">
            View Orders
          </button>
          <button className="bg-purple-500 hover:bg-purple-700 text-white py-3 px-4 rounded">
            Customer Reports
          </button>
          <button className="bg-orange-500 hover:bg-orange-700 text-white py-3 px-4 rounded">
            Sales Analytics
          </button>
        </div>
      </div>
    </div>
  );
};

export default BusinessOwnerDashboard;


