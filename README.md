# ShopHub E-commerce Platform

A full-featured e-commerce platform built with **React** frontend and **Spring Boot** backend, featuring role-based access control, shopping cart functionality, payment processing, and admin stock management.

## 🚀 Features

### ✅ Authentication & Authorization
- **Registration System**: Complete registration form with role selection (Customer/Business Owner)
- **Login System**: Secure login with role-based access control
- **Demo Credentials**: 
  - Customer: `Isingizwe` / `Kay<PERSON>@361`
  - Business Owner: `businessowner` / `password`

### ✅ E-commerce Functionality
- **Product Catalog**: 8 pre-loaded products with categories (Electronics, Accessories)
- **Shopping Cart**: Real-time cart updates with add/remove functionality
- **Search & Filter**: Product search and category filtering
- **Stock Management**: Real-time stock tracking and updates
- **Payment Processing**: Complete checkout with payment simulation
- **Order Management**: Order tracking and status management

### ✅ Role-Based Dashboards
- **Customer Dashboard**: Personal shopping experience with cart and order history
- **Business Owner Dashboard**: Analytics, inventory management, and order monitoring
- **Stock Management**: Admin can update product inventory in real-time

### ✅ Business Information
- **Contact**: +250785910639
- **Location**: Kigali, Kicukiro District, Rwanda
- **Copyright**: © 2025 ShopHub

### ✅ Technical Features
- **Responsive Design**: Works on desktop and mobile devices
- **Professional UI/UX**: Modern gradient design with card-based layout
- **Database Support**: MySQL (primary) with H2 fallback for development
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Maven Build System**: Easy dependency management and building

## 📋 Prerequisites

Before running the application, ensure you have the following installed:

### Required Software
1. **Java 17 or higher**
   ```bash
   java -version
   ```

2. **Maven 3.6 or higher**
   ```bash
   mvn -version
   ```

3. **Node.js 18 or higher**
   ```bash
   node -version
   ```

4. **pnpm** (Package Manager)
   ```bash
   npm install -g pnpm
   ```

5. **MySQL 8.0 or higher** (Optional - H2 fallback available)
   - Download and install MySQL from [https://dev.mysql.com/downloads/](https://dev.mysql.com/downloads/)
   - Install MySQL Workbench for database management

## 🗄️ Database Setup

### Option 1: MySQL Database (Recommended for Production)

1. **Install MySQL and MySQL Workbench**
   - Download from [MySQL Official Website](https://dev.mysql.com/downloads/)
   - Install MySQL Server and MySQL Workbench

2. **Create Database**
   - Open MySQL Workbench
   - Connect to your MySQL server
   - Run the provided SQL script:
   ```sql
   CREATE DATABASE IF NOT EXISTS shophub_db;
   USE shophub_db;
   ```

3. **Configure Database Connection**
   - Open `shophub-backend/src/main/resources/application.properties`
   - Update the MySQL configuration:
   ```properties
   spring.datasource.url=*********************************************************************************************************************************
   spring.datasource.username=root
   spring.datasource.password=your_mysql_password
   ```

### Option 2: H2 Database (Development/Testing)

If MySQL is not available, the application will automatically use H2 in-memory database:

1. **Run with H2 Profile**
   ```bash
   cd shophub-backend
   mvn spring-boot:run -Dspring-boot.run.profiles=h2
   ```

2. **Access H2 Console** (Optional)
   - URL: http://localhost:8080/h2-console
   - JDBC URL: `jdbc:h2:mem:shophubdb`
   - Username: `sa`
   - Password: `password`

## 🚀 Running the Application

### Step 1: Start the Backend (Spring Boot)

1. **Navigate to backend directory**
   ```bash
   cd shophub-backend
   ```

2. **Build the application**
   ```bash
   mvn clean install
   ```

3. **Run the backend server**
   
   **For MySQL:**
   ```bash
   mvn spring-boot:run
   ```
   
   **For H2 (if MySQL not available):**
   ```bash
   mvn spring-boot:run -Dspring-boot.run.profiles=h2
   ```

4. **Verify backend is running**
   - Backend will start on: http://localhost:8080
   - Check logs for "Started ShopHubBackendApplication"
   - API endpoints will be available at: http://localhost:8080/api/

### Step 2: Start the Frontend (React)

1. **Open a new terminal and navigate to frontend directory**
   ```bash
   cd shophub-frontend
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Start the development server**
   ```bash
   pnpm run dev
   ```

4. **Access the application**
   - Frontend will start on: http://localhost:5173
   - Open your browser and navigate to: http://localhost:5173

## 🧪 Testing the Application

### 1. Test User Authentication
- Navigate to http://localhost:5173
- Click "Login"
- Use demo credentials:
  - Customer: `Isingizwe` / `Kayumba@361`
  - Business Owner: `businessowner` / `password`

### 2. Test Customer Features
1. **Login as Customer** (`Isingizwe` / `Kayumba@361`)
2. **Browse Products**: View the product catalog
3. **Search Products**: Use the search bar to find specific items
4. **Filter by Category**: Select different categories from dropdown
5. **Add to Cart**: Click "Add to Cart" on any product
6. **View Cart**: Click "Cart" in navigation to see added items
7. **Checkout**: Click "Checkout" to process payment
8. **View Dashboard**: Access customer dashboard for order history

### 3. Test Business Owner Features
1. **Login as Business Owner** (`businessowner` / `password`)
2. **View Analytics**: Check sales metrics and order statistics
3. **Manage Inventory**: Update product stock quantities
4. **Monitor Orders**: View recent orders and customer information
5. **Business Information**: Review contact and business details

### 4. Test Cart Functionality
- **Add Items**: Add multiple products to cart
- **Remove Items**: Remove individual items from cart
- **Clear Cart**: Clear entire cart at once
- **Real-time Updates**: Cart count updates immediately
- **Persistent Cart**: Cart persists during session

### 5. Test Payment Processing
- **Complete Checkout**: Process payment for cart items
- **Owner Notification**: Business owner receives order notifications
- **Stock Updates**: Product stock decreases after successful orders

## 🏗️ Building for Production

### Build Backend
```bash
cd shophub-backend
mvn clean package
```
- JAR file will be created in `target/shophub-backend-0.0.1-SNAPSHOT.jar`

### Build Frontend
```bash
cd shophub-frontend
pnpm run build
```
- Production files will be created in `dist/` directory

## 📁 Project Structure

```
shophub-ecommerce/
├── shophub-backend/          # Spring Boot Backend
│   ├── src/main/java/        # Java source code
│   │   └── com/example/shophub/
│   │       ├── controller/   # REST API controllers
│   │       ├── model/        # JPA entities
│   │       ├── repository/   # Data repositories
│   │       ├── service/      # Business logic
│   │       ├── config/       # Configuration classes
│   │       └── util/         # Utility classes
│   ├── src/main/resources/   # Configuration files
│   │   ├── application.properties
│   │   └── application-h2.properties
│   └── pom.xml              # Maven dependencies
├── shophub-frontend/         # React Frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── App.jsx          # Main application component
│   │   └── App.css          # Styling
│   ├── public/              # Static assets
│   └── package.json         # npm dependencies
├── setup-mysql.sql          # MySQL setup script
└── README.md               # This file
```

## 🔧 Configuration

### Backend Configuration
- **Port**: 8080 (configurable in `application.properties`)
- **Database**: MySQL (primary), H2 (fallback)
- **CORS**: Configured for frontend origins
- **Security**: Spring Security with custom authentication

### Frontend Configuration
- **Port**: 5173 (Vite default)
- **API Base URL**: http://localhost:8080
- **Build Tool**: Vite
- **Package Manager**: pnpm

## 🐛 Troubleshooting

### Common Issues

1. **Backend won't start**
   - Check Java version: `java -version` (requires Java 17+)
   - Check Maven version: `mvn -version`
   - Verify MySQL is running (if using MySQL)
   - Try H2 profile: `mvn spring-boot:run -Dspring-boot.run.profiles=h2`

2. **Frontend won't start**
   - Check Node.js version: `node -version` (requires 18+)
   - Install pnpm: `npm install -g pnpm`
   - Clear cache: `pnpm store prune`
   - Reinstall dependencies: `rm -rf node_modules && pnpm install`

3. **Database connection issues**
   - Verify MySQL is running: `sudo systemctl status mysql`
   - Check database credentials in `application.properties`
   - Use H2 as fallback: Add `-Dspring-boot.run.profiles=h2` to run command

4. **CORS errors**
   - Ensure backend is running on port 8080
   - Check CORS configuration in `application.properties`
   - Verify frontend is accessing correct backend URL

5. **Cart not working**
   - Check browser console for JavaScript errors
   - Verify user is logged in as CUSTOMER role
   - Check network tab for API call failures
   - Ensure backend APIs are responding correctly

### Port Conflicts
If default ports are in use:

**Backend (8080):**
```properties
# In application.properties
server.port=8081
```

**Frontend (5173):**
```bash
pnpm run dev --port 3000
```

## 📞 Support

For technical support or questions:
- **Contact**: +250785910639
- **Location**: Kigali, Kicukiro District, Rwanda
- **Email**: <EMAIL>

## 📄 License

© 2025 ShopHub. All rights reserved.

---

**Note**: This application is configured for development and demonstration purposes. For production deployment, additional security measures, environment-specific configurations, and proper database setup are recommended.

