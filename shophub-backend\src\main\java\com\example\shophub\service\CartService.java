package com.example.shophub.service;

import com.example.shophub.model.CartItem;
import com.example.shophub.model.Product;
import com.example.shophub.model.User;
import com.example.shophub.repository.CartItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CartService {

    @Autowired
    private CartItemRepository cartItemRepository;

    @Autowired
    private ProductService productService;

    public List<CartItem> getCartItems(User user) {
        return cartItemRepository.findByUser(user);
    }

    public CartItem addToCart(User user, Long productId, Integer quantity) {
        Optional<Product> productOpt = productService.getProductById(productId);
        if (productOpt.isPresent()) {
            Product product = productOpt.get();
            Optional<CartItem> existingItem = cartItemRepository.findByUserAndProductId(user, productId);
            
            if (existingItem.isPresent()) {
                CartItem item = existingItem.get();
                item.setQuantity(item.getQuantity() + quantity);
                return cartItemRepository.save(item);
            } else {
                CartItem newItem = new CartItem(user, product, quantity);
                return cartItemRepository.save(newItem);
            }
        }
        return null;
    }

    public void removeFromCart(User user, Long cartItemId) {
        cartItemRepository.deleteById(cartItemId);
    }

    public void clearCart(User user) {
        cartItemRepository.deleteByUser(user);
    }
}

