-- MySQL Setup Script for ShopHub E-commerce Platform
-- Run this script in MySQL Workbench to set up the database

-- Create database
CREATE DATABASE IF NOT EXISTS shophub_db;
USE shophub_db;

-- Create user (optional - you can use root)
-- CREATE USER IF NOT EXISTS 'shophub_user'@'localhost' IDENTIFIED BY 'shophub_password';
-- GRANT ALL PRIVILEGES ON shophub_db.* TO 'shophub_user'@'localhost';
-- FLUSH PRIVILEGES;

-- The tables will be automatically created by Spring Boot JPA when you run the application
-- This script just ensures the database exists

-- Verify database creation
SHOW DATABASES;
SELECT 'ShopHub database setup completed successfully!' AS status;

