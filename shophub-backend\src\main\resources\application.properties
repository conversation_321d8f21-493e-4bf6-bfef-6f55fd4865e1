# Server Configuration
server.port=8080

# MySQL Database Configuration (Primary)
spring.datasource.url=*********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# H2 Database Configuration (Fallback for development)
# Uncomment these lines and comment MySQL configuration above if MySQL is not available
#spring.datasource.url=jdbc:h2:mem:shophubdb
#spring.datasource.driverClassName=org.h2.Driver
#spring.datasource.username=sa
#spring.datasource.password=password
#spring.h2.console.enabled=true

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true

# CORS Configuration
spring.web.cors.allowed-origins=http://localhost:5173,http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Logging Configuration
logging.level.com.example.shophub=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Application Configuration
app.name=ShopHub E-commerce Platform
app.version=1.0.0
app.description=Full-featured e-commerce platform with React frontend and Spring Boot backend

