import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const ProductDetail = ({ user }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`http://localhost:8080/api/products/${id}`);
      if (response.ok) {
        const data = await response.json();
        setProduct(data);
      } else {
        navigate('/');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      navigate('/');
    }
  };

  const addToCart = async () => {
    if (!user || user.role !== 'CUSTOMER') {
      alert('Please login as a customer to add items to cart');
      return;
    }

    try {
      const response = await fetch('http://localhost:8080/api/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: parseInt(id),
          quantity: quantity
        }),
      });

      if (response.ok) {
        alert(`${quantity} item(s) added to cart!`);
      } else {
        alert('Failed to add product to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add product to cart');
    }
  };

  if (!product) {
    return <div className="text-center">Loading...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto">
      <button
        onClick={() => navigate('/')}
        className="mb-4 bg-gray-500 hover:bg-gray-700 text-white py-2 px-4 rounded"
      >
        ← Back to Products
      </button>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="md:flex">
          <div className="md:w-1/2">
            <img
              src={product.imageUrl}
              alt={product.name}
              className="w-full h-96 object-cover"
            />
          </div>
          <div className="md:w-1/2 p-6">
            <h1 className="text-3xl font-bold mb-4">{product.name}</h1>
            <p className="text-gray-600 mb-4">{product.description}</p>
            <p className="text-2xl font-bold text-blue-600 mb-4">${product.price}</p>
            <p className="text-lg mb-4">Category: <span className="font-semibold">{product.category}</span></p>
            <p className="text-lg mb-6">Stock: <span className="font-semibold">{product.stock} available</span></p>
            
            {user && user.role === 'CUSTOMER' && (
              <div className="flex items-center gap-4 mb-6">
                <label className="font-semibold">Quantity:</label>
                <input
                  type="number"
                  min="1"
                  max={product.stock}
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                />
                <button
                  onClick={addToCart}
                  className="bg-blue-500 hover:bg-blue-700 text-white py-2 px-6 rounded"
                >
                  Add to Cart
                </button>
              </div>
            )}
            
            {!user && (
              <p className="text-gray-500">Please login to add items to cart.</p>
            )}
            
            {user && user.role !== 'CUSTOMER' && (
              <p className="text-gray-500">Only customers can add items to cart.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;

