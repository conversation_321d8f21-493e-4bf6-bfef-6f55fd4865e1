package com.example.shophub.util;

import com.example.shophub.model.Product;
import com.example.shophub.model.User;
import com.example.shophub.repository.ProductRepository;
import com.example.shophub.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class InitialDataLoader implements CommandLineRunner {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductRepository productRepository;

    @Override
    public void run(String... args) throws Exception {
        // Create users
        if (userService.findByUsername("Isingizwe").isEmpty()) {
            userService.registerUser("Isingizwe", "Kayumba@361", "CUSTOMER");
        }
        if (userService.findByUsername("businessowner").isEmpty()) {
            userService.registerUser("businessowner", "password", "BUSINESS_OWNER");
        }

        // Create products
        if (productRepository.count() == 0) {
            productRepository.save(new Product("Laptop", "Powerful laptop for work and gaming", 1200.00, "Electronics", "https://via.placeholder.com/150", 10));
            productRepository.save(new Product("Smartphone", "Latest model with advanced features", 800.00, "Electronics", "https://via.placeholder.com/150", 25));
            productRepository.save(new Product("Headphones", "Noise-cancelling over-ear headphones", 150.00, "Accessories", "https://via.placeholder.com/150", 50));
            productRepository.save(new Product("Keyboard", "Mechanical keyboard with RGB lighting", 75.00, "Accessories", "https://via.placeholder.com/150", 30));
            productRepository.save(new Product("Mouse", "Ergonomic wireless mouse", 40.00, "Accessories", "https://via.placeholder.com/150", 40));
            productRepository.save(new Product("Monitor", "27-inch 4K UHD monitor", 300.00, "Electronics", "https://via.placeholder.com/150", 15));
            productRepository.save(new Product("Webcam", "Full HD webcam for video calls", 60.00, "Accessories", "https://via.placeholder.com/150", 20));
            productRepository.save(new Product("Printer", "All-in-one inkjet printer", 100.00, "Electronics", "https://via.placeholder.com/150", 12));
        }
    }
}

