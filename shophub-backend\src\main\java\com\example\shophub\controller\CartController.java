package com.example.shophub.controller;

import com.example.shophub.model.CartItem;
import com.example.shophub.model.User;
import com.example.shophub.model.Order;
import com.example.shophub.service.CartService;
import com.example.shophub.service.UserService;
import com.example.shophub.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/cart")
@CrossOrigin(origins = "http://localhost:5173")
public class CartController {

    @Autowired
    private CartService cartService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrderService orderService;

    @GetMapping("/{userId}")
    public ResponseEntity<List<CartItem>> getCartItems(@PathVariable Long userId) {
        // Find user by ID instead of hardcoded username
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isPresent()) {
            return ResponseEntity.ok(cartService.getCartItems(userOpt.get()));
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping("/add")
    public ResponseEntity<CartItem> addToCart(@RequestBody Map<String, Object> request) {
        Long productId = Long.valueOf(request.get("productId").toString());
        Integer quantity = Integer.valueOf(request.get("quantity").toString());
        Long userId = Long.valueOf(request.get("userId").toString());

        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isPresent()) {
            CartItem item = cartService.addToCart(userOpt.get(), productId, quantity);
            return ResponseEntity.ok(item);
        }
        return ResponseEntity.badRequest().build();
    }

    @DeleteMapping("/{userId}/{cartItemId}")
    public ResponseEntity<Void> removeFromCart(@PathVariable Long userId, @PathVariable Long cartItemId) {
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isPresent()) {
            cartService.removeFromCart(userOpt.get(), cartItemId);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.badRequest().build();
    }

    @DeleteMapping("/clear/{userId}")
    public ResponseEntity<Void> clearCart(@PathVariable Long userId) {
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isPresent()) {
            cartService.clearCart(userOpt.get());
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.badRequest().build();
    }

    @PostMapping("/checkout/{userId}")
    public ResponseEntity<Order> checkout(@PathVariable Long userId) {
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isPresent()) {
            try {
                Order order = orderService.createOrderFromCart(userOpt.get());
                return ResponseEntity.ok(order);
            } catch (RuntimeException e) {
                return ResponseEntity.badRequest().body(null); // Handle empty cart or other errors
            }
        }
        return ResponseEntity.badRequest().body(null);
    }
}


