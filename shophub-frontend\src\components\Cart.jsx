import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const Cart = ({ user, onCartUpdate }) => {
  const [cartItems, setCartItems] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (user) {
      fetchCartItems();
    }
  }, [user]);

  useEffect(() => {
    calculateTotal();
  }, [cartItems]);

  const fetchCartItems = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8080/api/cart/${user.id}`);
      if (response.ok) {
        const data = await response.json();
        setCartItems(data);
      }
    } catch (error) {
      console.error('Error fetching cart items:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateTotal = () => {
    const totalAmount = cartItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity);
    }, 0);
    setTotal(totalAmount);
  };

  const removeFromCart = async (cartItemId) => {
    try {
      const response = await fetch(`http://localhost:8080/api/cart/${user.id}/${cartItemId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCartItems(cartItems.filter(item => item.id !== cartItemId));
        if (onCartUpdate) {
          onCartUpdate();
        }
      } else {
        alert('Failed to remove item from cart');
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
      alert('Failed to remove item from cart');
    }
  };

  const clearCart = async () => {
    if (!window.confirm('Are you sure you want to clear your cart?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:8080/api/cart/clear/${user.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCartItems([]);
        if (onCartUpdate) {
          onCartUpdate();
        }
      } else {
        alert('Failed to clear cart');
      }
    } catch (error) {
      console.error('Error clearing cart:', error);
      alert('Failed to clear cart');
    }
  };

  const checkout = async () => {
    if (cartItems.length === 0) {
      alert('Your cart is empty');
      return;
    }
    
    setProcessing(true);
    
    try {
      // Step 1: Create the order
      const orderResponse = await fetch(`http://localhost:8080/api/cart/checkout/${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!orderResponse.ok) {
        throw new Error('Failed to create order');
      }

      const order = await orderResponse.json();
      
      // Step 2: Process payment for the order
      const paymentResponse = await fetch(`http://localhost:8080/api/payment/process?orderId=${order.id}`, {
        method: 'POST',
      });

      if (paymentResponse.ok) {
        alert(`Payment successful! Order #${order.id} has been placed.`);
        setCartItems([]);
        if (onCartUpdate) {
          onCartUpdate();
        }
      } else {
        const errorText = await paymentResponse.text();
        alert(`Payment failed: ${errorText}`);
      }
    } catch (error) {
      console.error('Checkout error:', error);
      alert(`Checkout failed: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  if (!user || user.role !== 'CUSTOMER') {
    return (
      <div className="form-container">
        <h2 className="form-title">Access Denied</h2>
        <p>Please login as a customer to view your cart.</p>
        <Link to="/login" className="btn btn-primary">Login</Link>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="cart-container">
      <h1 className="cart-title">Shopping Cart</h1>
      
      {cartItems.length === 0 ? (
        <div className="cart-empty">
          <p className="cart-empty-text">Your cart is empty</p>
          <Link to="/" className="btn btn-primary">Continue Shopping</Link>
        </div>
      ) : (
        <>
          <div className="cart-items">
            {cartItems.map(item => (
              <div key={item.id} className="cart-item">
                <img
                  src={item.product.imageUrl || '/api/placeholder/80/80'}
                  alt={item.product.name}
                  className="cart-item-image"
                  onError={(e) => {
                    e.target.src = '/api/placeholder/80/80';
                  }}
                />
                <div className="cart-item-info">
                  <h3 className="cart-item-name">{item.product.name}</h3>
                  <p className="cart-item-price">${item.product.price}</p>
                </div>
                <div className="cart-item-actions">
                  <span className="cart-item-quantity">Qty: {item.quantity}</span>
                  <span className="cart-item-total">
                    ${(item.product.price * item.quantity).toFixed(2)}
                  </span>
                  <button
                    onClick={() => removeFromCart(item.id)}
                    className="btn btn-danger"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="cart-summary">
            <div className="cart-total">
              <span className="cart-total-label">Total:</span>
              <span className="cart-total-amount">${total.toFixed(2)}</span>
            </div>
            <div className="cart-actions">
              <button
                onClick={clearCart}
                className="btn btn-secondary"
                disabled={processing}
              >
                Clear Cart
              </button>
              <button
                onClick={checkout}
                className="btn btn-success"
                disabled={processing}
              >
                {processing ? 'Processing...' : 'Checkout'}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Cart;

